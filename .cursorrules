当你没有足够的上下文时，先阅读现有代码，获取足够的上下文。如果还信息还不足，请不要编译代码，请先告诉我需要什么信息。
使用思维调用链，不要跳过思维调用链。
; 学会把一个复杂的问题分解为多个简单的问题，然后逐步解决。

开发完一个功能，给出下一个功能的开发计划。
; 如果一个文件大于1000行，请先按模块功能拆分文件。
每次写完代码要检查是否写入成功，如果没有就换一种命令或则mcp命令。要保证写入成功，最多重试3次
代码里涉及到的跳转链接的页面
先创建文件，再写入代码。
你询问是否同意，使用1表示同意，使用0表示不同意。
后端启动命令：lsof -ti :8081 | xargs kill -9 && cd server && go run main.go
在解决bug时，直接修改代码，不要问我是否同意。
你可以用命令行执行sql

启动程序的命令在外部窗口打开

修改完后端代码，帮我重启服务
你用燕追风的账号，密码是123456
数据库连接信息
host: 127.0.0.1
port: 3306
user: root
password: 123456
database: beauty

遵循规范
1. 遵循Go语言规范
2. 遵循uniapp规范
8. 遵循现有的项目结构
9. 遵循现有的项目规范
10. 遵循现有的项目配置
使用了正确的数据库模型命名（DO 后缀）
使用了 Query() 方法进行数据库操作
修正了 xerror 的导入路径和使用方式
统一了错误处理方式

不要帮我编译