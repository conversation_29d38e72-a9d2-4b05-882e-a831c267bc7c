<template>
  <view class="card-detail-page">
    <!-- 顶部导航栏 -->
    <beauty-navbar 
      title="套餐卡详情" 
      :showBack="true" 
      :showSearch="false"
      ref="navbar"
    />
    
    <!-- 页面内容 -->
    <view class="card-detail-content" :style="{ paddingTop: navbarHeight + 'px' }">
      <!-- 图片轮播 -->
      <swiper 
        class="card-swiper" 
        :indicator-dots="true" 
        :autoplay="true" 
        v-if="card.images && card.images.length > 0"
      >
        <swiper-item v-for="(img, idx) in card.images" :key="idx">
          <image :src="img" class="card-image" mode="aspectFill" />
        </swiper-item>
      </swiper>
      
      <!-- 默认图片 -->
      <view v-else class="default-image">
        <image :src="defaultImage" class="card-image" mode="aspectFill" />
      </view>
      
      <!-- 卡片基本信息 -->
      <view class="card-info">
        <view class="card-header">
          <text class="card-name">{{ card.name }}</text>
          <view class="card-badges">
            <view v-if="card.is_hot" class="badge hot-badge">热门</view>
            <view v-if="card.is_new" class="badge new-badge">新品</view>
          </view>
        </view>
        <text class="card-subtitle">{{ card.subtitle }}</text>
        <view class="card-price-row">
          <text class="card-price">￥{{ card.price }}</text>
          <text class="card-original-price" v-if="card.original_price > card.price">￥{{ card.original_price }}</text>
        </view>
        <view class="card-meta">
          <text class="card-valid">有效期：购买后{{ card.valid_days }}天</text>
          <text class="card-sales">已售{{ card.sales_count || 0 }}</text>
        </view>
      </view>
      
      <!-- 套餐卡介绍 -->
      <view class="card-section">
        <text class="section-title">套餐卡介绍</text>
        <text class="card-desc">{{ card.description || '暂无介绍' }}</text>
      </view>
      
      <!-- 包含服务 -->
      <view class="card-section" v-if="serviceItems && serviceItems.length > 0">
        <text class="section-title">包含服务</text>
        <view class="service-list">
          <view class="service-item" v-for="(item, idx) in serviceItems" :key="idx">
            <view class="service-info">
              <text class="service-name">{{ item.serviceName }}</text>
              <text class="service-price">¥{{ item.price }}</text>
            </view>
            <view class="service-times" v-if="item.times !== -1">
              <text class="times-text">{{ item.times }}次</text>
            </view>
            <view class="service-times" v-else>
              <text class="times-text">不限次数</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 使用说明 -->
      <view class="card-section">
        <text class="section-title">使用说明</text>
        <view class="usage-list">
          <view class="usage-item">
            <text class="usage-icon">📅</text>
            <text class="usage-text">购买后{{ card.valid_days }}天内有效</text>
          </view>
          <view class="usage-item">
            <text class="usage-icon">🎯</text>
            <text class="usage-text">{{ getCardTypeText(card.card_type) }}</text>
          </view>
          <view class="usage-item">
            <text class="usage-icon">💳</text>
            <text class="usage-text">可在门店出示使用</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部购买按钮 -->
    <view class="card-detail-footer">
      <button class="buy-btn" @click="buyCard" :disabled="loading">
        {{ loading ? '加载中...' : '立即购买' }}
      </button>
    </view>
  </view>
</template>

<script>
import { getCardDetail } from '@/api/beauty/card.js'
import BeautyNavbar from '@/components/beauty-navbar/beauty-navbar.vue'

export default {
  name: 'CardDetail',
  components: {
    BeautyNavbar
  },
  data() {
    return {
      cardId: null,
      card: {},
      loading: false,
      navbarHeight: 64,
      defaultImage: '/static/beauty/banner1.jpg'
    }
  },
  computed: {
    serviceItems() {
      if (!this.card.service_items) return []
      
      try {
        if (typeof this.card.service_items === 'string') {
          return JSON.parse(this.card.service_items)
        }
        return this.card.service_items
      } catch (error) {
        console.warn('解析服务项目失败:', error)
        return []
      }
    }
  },
  onLoad(options) {
    this.cardId = options.id
    if (this.cardId) {
      this.fetchCardDetail()
    } else {
      uni.showToast({ title: '参数错误', icon: 'none' })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
    this.calculateNavbarHeight()
  },
  methods: {
    async fetchCardDetail() {
      this.loading = true
      try {
        const res = await getCardDetail({ id: this.cardId })
        if (res.code === 200 && res.data) {
          this.card = res.data
          // 处理图片数据
          if (this.card.images) {
            try {
              if (typeof this.card.images === 'string') {
                this.card.images = JSON.parse(this.card.images)
              }
            } catch (error) {
              console.warn('解析图片数据失败:', error)
              this.card.images = [this.defaultImage]
            }
          } else {
            this.card.images = [this.defaultImage]
          }
        } else {
          uni.showToast({ title: res.message || '获取详情失败', icon: 'none' })
        }
      } catch (error) {
        console.error('获取套餐卡详情失败:', error)
        uni.showToast({ title: '网络错误', icon: 'none' })
      } finally {
        this.loading = false
      }
    },
    buyCard() {
      if (this.loading) return
      
      // 检查登录状态
      const token = uni.getStorageSync('token')
      if (!token) {
        uni.showToast({ title: '请先登录', icon: 'none' })
        setTimeout(() => {
          uni.navigateTo({ url: '/pages/login/login' })
        }, 1500)
        return
      }
      
      // 跳转到购买确认页面
      uni.navigateTo({
        url: `/pages/beauty/card/confirm?id=${this.cardId}`
      })
    },
    getCardTypeText(type) {
      const typeMap = {
        1: '次数卡：按次数使用',
        2: '金额卡：按金额使用',
        3: '混合卡：次数和金额混合使用'
      }
      return typeMap[type] || '套餐卡'
    },
    calculateNavbarHeight() {
      try {
        const systemInfo = uni.getSystemInfoSync()
        const statusBarHeight = systemInfo.statusBarHeight || 20
        const navbarContentHeight = 44
        this.navbarHeight = statusBarHeight + navbarContentHeight
      } catch (error) {
        console.warn('获取系统信息失败:', error)
        this.navbarHeight = 64
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.card-detail-page {
  background: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 100px;
}

.card-detail-content {
  /* 动态padding-top */
}

.card-swiper, .default-image {
  width: 100%;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 16px;
}

.card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-info {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.card-name {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.card-badges {
  display: flex;
  gap: 6px;
}

.badge {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  color: #fff;
  font-weight: bold;
}

.hot-badge {
  background: #ff4757;
}

.new-badge {
  background: #2ed573;
}

.card-subtitle {
  color: #999;
  font-size: 14px;
  margin-bottom: 12px;
  display: block;
}

.card-price-row {
  display: flex;
  align-items: baseline;
  gap: 10px;
  margin-bottom: 12px;
}

.card-price {
  color: #ff4757;
  font-size: 24px;
  font-weight: bold;
}

.card-original-price {
  color: #bbb;
  text-decoration: line-through;
  font-size: 16px;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-valid, .card-sales {
  color: #666;
  font-size: 12px;
}

.card-section {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.card-desc {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

.service-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.service-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.service-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.service-price {
  font-size: 14px;
  color: #ff6b9d;
  font-weight: bold;
}

.service-times {
  display: flex;
  align-items: center;
}

.times-text {
  font-size: 14px;
  color: #666;
  background: #fff;
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.usage-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.usage-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.usage-icon {
  font-size: 16px;
}

.usage-text {
  font-size: 14px;
  color: #666;
}

.card-detail-footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  box-shadow: 0 -2px 12px rgba(0,0,0,0.08);
  padding: 16px 20px;
  display: flex;
  justify-content: center;
}

.buy-btn {
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  border-radius: 24px;
  padding: 14px 48px;
  border: none;
  
  &:disabled {
    opacity: 0.6;
  }
}
</style> 