<template>
  <view class="booking-detail">
    <!-- 统一导航栏 -->
    <beauty-navbar 
      title="预约详情" 
      :show-back="true"
    ></beauty-navbar>
    
    <!-- 预约状态 -->
    <view class="status-header" style="margin-top: 88px;">
      <text class="status-icon" :style="{ color: getStatusColor() }">{{ getStatusIcon() }}</text>
      <text class="status-title">{{ getStatusText() }}</text>
      <text class="status-desc">{{ getStatusDesc() }}</text>
    </view>
    
    <!-- 预约信息 -->
    <view class="booking-info">
      <view class="info-header">
        <text class="header-title">预约信息</text>
        <view class="booking-id">
          <text>预约编号：{{ booking.id }}</text>
        </view>
      </view>
      
      <view class="info-content">
        <view class="info-row">
          <text class="info-label">服务项目</text>
          <text class="info-value">{{ booking.serviceName }}</text>
        </view>
        
        <view class="info-row" v-if="booking.technicianName">
          <text class="info-label">指定技师</text>
          <text class="info-value">{{ booking.technicianName }}</text>
        </view>
        
        <view class="info-row">
          <text class="info-label">预约时间</text>
          <text class="info-value">{{ booking.bookingDate }} {{ booking.bookingTime }}</text>
        </view>
        
        <view class="info-row">
          <text class="info-label">服务时长</text>
          <text class="info-value">{{ booking.duration }}分钟</text>
        </view>
        
        <view class="info-row">
          <text class="info-label">联系方式</text>
          <text class="info-value">{{ booking.customerPhone }}</text>
        </view>
        
        <view class="info-row">
          <text class="info-label">支付金额</text>
          <text class="info-value price">¥{{ booking.finalPrice }}</text>
        </view>
        
        <view class="info-row">
          <text class="info-label">支付状态</text>
          <text class="info-value" :class="getPaymentStatusClass()">{{ getPaymentStatusText() }}</text>
        </view>
        
        <view class="info-row" v-if="booking.customerNote">
          <text class="info-label">备注信息</text>
          <text class="info-value">{{ booking.customerNote }}</text>
        </view>
      </view>
    </view>
    
    <!-- 服务进度 -->
    <view class="progress-section" v-if="booking.status !== 'cancelled'">
      <view class="progress-header">
        <text class="header-title">服务进度</text>
      </view>
      <view class="progress-content">
        <view class="progress-step" :class="{ active: getStepStatus('pending') }">
          <view class="step-icon">
            <text class="step-icon-text" :style="{ color: getStepStatus('pending') ? '#FFB6C1' : '#ddd' }">⏰</text>
          </view>
          <view class="step-info">
            <text class="step-title">预约提交</text>
            <text class="step-desc">您的预约已提交，等待确认</text>
          </view>
        </view>
        
        <view class="progress-step" :class="{ active: getStepStatus('confirmed') }">
          <view class="step-icon">
            <text class="step-icon-text" :style="{ color: getStepStatus('confirmed') ? '#FFB6C1' : '#ddd' }">✓</text>
          </view>
          <view class="step-info">
            <text class="step-title">预约确认</text>
            <text class="step-desc">预约已确认，请按时到店</text>
          </view>
        </view>
        
        <view class="progress-step" :class="{ active: getStepStatus('in_service') }">
          <view class="step-icon">
            <text class="step-icon-text" :style="{ color: getStepStatus('in_service') ? '#FFB6C1' : '#ddd' }">▶️</text>
          </view>
          <view class="step-info">
            <text class="step-title">服务中</text>
            <text class="step-desc">正在为您提供服务</text>
          </view>
        </view>
        
        <view class="progress-step" :class="{ active: getStepStatus('completed') }">
          <view class="step-icon">
            <text class="step-icon-text" :style="{ color: getStepStatus('completed') ? '#FFB6C1' : '#ddd' }">🎉</text>
          </view>
          <view class="step-info">
            <text class="step-title">服务完成</text>
            <text class="step-desc">服务已完成，感谢您的光临</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 联系方式 -->
    <view class="contact-section">
      <view class="contact-header">
        <text class="header-title">联系方式</text>
      </view>
      <view class="contact-content">
        <view class="contact-item" @click="callPhone">
          <text class="contact-icon">📞</text>
          <text class="contact-text">客服电话</text>
          <text class="arrow-icon">></text>
        </view>
        
        <view class="contact-item" @click="openMap">
          <text class="contact-icon">📍</text>
          <text class="contact-text">门店地址</text>
          <text class="arrow-icon">></text>
        </view>
      </view>
    </view>
    
    <!-- 状态变更历史 -->
    <view class="status-history" v-if="booking.statusHistory && booking.statusHistory.length > 0">
      <view class="history-header">
        <text class="header-title">状态变更记录</text>
      </view>
      <view class="history-content">
        <view 
          class="history-item" 
          v-for="(history, index) in booking.statusHistory" 
          :key="index"
        >
          <view class="history-time">{{ formatTime(history.timestamp) }}</view>
          <view class="history-status">{{ history.statusText }}</view>
          <view class="history-reason" v-if="history.reason">{{ history.reason }}</view>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons" v-if="showActionButtons">
      <!-- 支付按钮 - 未支付状态显示 -->
      <button 
        v-if="booking.paymentStatus === 'unpaid' && booking.status !== 'cancelled'"
        class="btn btn-primary" 
        @click="goToPayment"
      >
        立即支付 ¥{{ booking.finalPrice }}
      </button>
      
      <button 
        v-if="booking.status === 'pending' || booking.status === 'confirmed'"
        class="btn btn-default" 
        @click="showRescheduleModal"
      >
        改期
      </button>
      
      <button 
        v-if="booking.status === 'pending' || booking.status === 'confirmed'"
        class="btn btn-default" 
        @click="cancelBooking"
      >
        取消预约
      </button>
      
      <button 
        v-if="booking.status === 'completed'"
        class="btn btn-primary" 
        @click="reviewBooking"
      >
        评价服务
      </button>
      
      <button 
        v-if="booking.status === 'cancelled' || booking.status === 'completed'"
        class="btn btn-primary" 
        @click="rebookService"
      >
        再次预约
      </button>
    </view>
    
    <!-- 改期模态框 -->
    <view class="reschedule-modal" v-if="showReschedule" @click="hideRescheduleModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">预约改期</text>
          <text class="modal-close" @click="hideRescheduleModal">×</text>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">选择新日期</text>
            <picker 
              mode="date" 
              :value="rescheduleData.newDate"
              :start="minDate"
              :end="maxDate"
              @change="onDateChange"
            >
              <view class="picker-input">
                <text>{{ rescheduleData.newDate || '请选择日期' }}</text>
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">选择新时间</text>
            <picker 
              mode="time" 
              :value="rescheduleData.newTime"
              @change="onTimeChange"
            >
              <view class="picker-input">
                <text>{{ rescheduleData.newTime || '请选择时间' }}</text>
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">改期原因</text>
            <textarea 
              v-model="rescheduleData.reason"
              placeholder="请输入改期原因（选填）"
              maxlength="200"
              class="form-textarea"
            ></textarea>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="btn btn-default" @click="hideRescheduleModal">取消</button>
          <button class="btn btn-primary" @click="confirmReschedule">确认改期</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getBookingDetail, cancelBooking, rescheduleBooking } from '@/api/beauty/index.js'

export default {
  name: 'BookingDetail',
  
  data() {
    return {
      bookingId: null,
      booking: {},
      showReschedule: false,
      rescheduleData: {
        newDate: '',
        newTime: '',
        reason: ''
      }
    }
  },
  
  computed: {
    showActionButtons() {
      return this.booking.status && this.booking.status !== 'in_service'
    },
    
    minDate() {
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      return tomorrow.toISOString().split('T')[0]
    },
    
    maxDate() {
      const maxDate = new Date()
      maxDate.setDate(maxDate.getDate() + 30)
      return maxDate.toISOString().split('T')[0]
    }
  },
  
  onLoad(options) {
    if (options.id) {
      this.bookingId = parseInt(options.id)
      this.loadBookingDetail()
    }
  },
  
  onShow() {
    // 页面显示时刷新数据，确保支付状态是最新的
    if (this.bookingId) {
      this.loadBookingDetail()
    }
  },
  
  methods: {
    async loadBookingDetail() {
      try {
        const result = await getBookingDetail(this.bookingId)
        const data = result.data || {}
        // 字段映射，保证页面展示字段完整
        this.booking = {
          id: data.id,
          serviceName: data.service_name,
          technicianName: data.technician_name,
          bookingDate: data.booking_date,
          bookingTime: data.start_time,
          duration: data.duration,
          finalPrice: data.final_price,
          contactName: data.contact_name,
          contactPhone: data.contact_phone,
          serviceDesc: data.service_desc,
          bookingStatus: data.booking_status,
          paymentStatus: data.payment_status,
          customerNote: data.special_requests,
          // 其他字段可按需补充
          status: data.booking_status,
          statusHistory: data.status_history || [],
          // ...如需展示更多字段可继续补充
        }
        // 设置页面标题
        uni.setNavigationBarTitle({
          title: `预约详情 - ${this.booking.serviceName}`
        })
      } catch (error) {
        console.error('加载预约详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },
    
    getStatusIcon() {
      const iconMap = {
        'pending': '⏳',
        'confirmed': '✅',
        'in_service': '🔄',
        'completed': '🎉',
        'cancelled': '❌'
      }
      return iconMap[this.booking.status] || '❓'
    },
    
    getStatusColor() {
      const colorMap = {
        'pending': '#ff9500',
        'confirmed': '#52c41a',
        'in_service': '#1890ff',
        'completed': '#52c41a',
        'cancelled': '#ff4d4f'
      }
      return colorMap[this.booking.status] || '#999'
    },
    
    getStatusText() {
      const textMap = {
        'pending': '等待确认',
        'confirmed': '预约已确认',
        'in_service': '服务进行中',
        'completed': '服务已完成',
        'cancelled': '预约已取消'
      }
      return textMap[this.booking.status] || '未知状态'
    },
    
    getStatusDesc() {
      const descMap = {
        'pending': '您的预约正在等待店家确认',
        'confirmed': '请按时到店享受服务',
        'in_service': '技师正在为您提供专业服务',
        'completed': '感谢您的光临，期待下次为您服务',
        'cancelled': '如有疑问，请联系客服'
      }
      return descMap[this.booking.status] || ''
    },
    
    getStepStatus(step) {
      const statusOrder = ['pending', 'confirmed', 'in_service', 'completed']
      const currentIndex = statusOrder.indexOf(this.booking.status)
      const stepIndex = statusOrder.indexOf(step)
      return currentIndex >= stepIndex
    },
    
    getPaymentStatusText() {
      const statusMap = {
        'unpaid': '未支付',
        'paid': '已支付',
        'refunded': '已退款',
        'partial_refunded': '部分退款'
      }
      return statusMap[this.booking.paymentStatus] || '未知状态'
    },
    
    getPaymentStatusClass() {
      const classMap = {
        'unpaid': 'payment-unpaid',
        'paid': 'payment-paid',
        'refunded': 'payment-refunded',
        'partial_refunded': 'payment-partial-refunded'
      }
      return classMap[this.booking.paymentStatus] || ''
    },
    
    callPhone() {
      uni.makePhoneCall({
        phoneNumber: '************'
      })
    },
    
    openMap() {
      uni.openLocation({
        latitude: 39.9042,
        longitude: 116.4074,
        name: '美丽时光美容院',
        address: '北京市朝阳区xxx路xxx号'
      })
    },
    
    async cancelBooking() {
      uni.showModal({
        title: '确认取消',
        content: '确定要取消这个预约吗？取消后不可恢复。',
        success: async (res) => {
          if (res.confirm) {
            // 显示取消原因选择
            uni.showActionSheet({
              itemList: ['时间冲突', '临时有事', '选择其他服务', '其他原因'],
              success: async (actionRes) => {
                const reasons = ['时间冲突', '临时有事', '选择其他服务', '其他原因']
                const cancelReason = reasons[actionRes.tapIndex]
                
                // 如果是其他原因，让用户输入
                if (cancelReason === '其他原因') {
                  uni.showModal({
                    title: '请输入取消原因',
                    editable: true,
                    placeholderText: '请输入取消原因',
                    success: async (modalRes) => {
                      if (modalRes.confirm) {
                        await this.performCancelBooking(modalRes.content || '其他原因')
                      }
                    }
                  })
                } else {
                  await this.performCancelBooking(cancelReason)
                }
              }
            })
          }
        }
      })
    },
    
    // 执行取消预约
    async performCancelBooking(cancelReason) {
      try {
        await cancelBooking({ 
          id: this.bookingId,
          cancel_reason: cancelReason
        })
        uni.showToast({
          title: '取消成功',
          icon: 'success'
        })
        this.loadBookingDetail()
      } catch (error) {
        console.error('取消预约失败:', error)
        uni.showToast({
          title: '取消失败',
          icon: 'none'
        })
      }
    },
    
    reviewBooking() {
      uni.navigateTo({
        url: `/pages/beauty/user/write-review?bookingId=${this.bookingId}`
      })
    },
    
    rebookService() {
      uni.navigateTo({
        url: `/pages/beauty/booking/technician?serviceId=${this.booking.serviceId}`
      })
    },

    goToPayment() {
      uni.navigateTo({
        url: `/pages/beauty/payment/index?bookingId=${this.bookingId}`
      })
    },
    
    showRescheduleModal() {
      this.showReschedule = true
      this.rescheduleData = {
        newDate: '',
        newTime: '',
        reason: ''
      }
    },
    
    hideRescheduleModal() {
      this.showReschedule = false
    },
    
    onDateChange(e) {
      this.rescheduleData.newDate = e.detail.value
    },
    
    onTimeChange(e) {
      this.rescheduleData.newTime = e.detail.value
    },
    
    async confirmReschedule() {
      if (!this.rescheduleData.newDate || !this.rescheduleData.newTime) {
        uni.showToast({
          title: '请选择新的日期和时间',
          icon: 'none'
        })
        return
      }
      
      try {
        uni.showLoading({
          title: '处理中...'
        })
        
        const result = await rescheduleBooking({
          id: this.bookingId,
          booking_date: this.rescheduleData.newDate,
          start_time: this.rescheduleData.newTime,
          reason: this.rescheduleData.reason
        })
        
        uni.hideLoading()
        
        if (result.code === 200) {
          uni.showToast({
            title: '改期成功',
            icon: 'success'
          })
          this.hideRescheduleModal()
          this.loadBookingDetail()
        } else if (result.data && result.data.conflict) {
          // 时间冲突，显示替代时间
          const alternatives = result.data.alternatives || []
          const alternativeText = alternatives.length > 0 
            ? `建议时间：${alternatives.join(', ')}`
            : '请选择其他时间'
          
          uni.showModal({
            title: '时间冲突',
            content: `${result.message}\n${alternativeText}`,
            showCancel: false
          })
        } else {
          uni.showToast({
            title: result.message || '改期失败',
            icon: 'none'
          })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('改期失败:', error)
        uni.showToast({
          title: '改期失败',
          icon: 'none'
        })
      }
    },
    
    formatTime(timeString) {
      const time = new Date(timeString)
      return time.toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.booking-detail {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

.status-header {
  background-color: #ffffff;
  text-align: center;
  padding: 60rpx 30rpx;
  margin-bottom: 20rpx;
  
  .status-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
    display: block;
  }
  
  .status-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 15rpx;
    display: block;
  }
  
  .status-desc {
    font-size: 26rpx;
    color: #666666;
    display: block;
  }
}

.booking-info, .progress-section, .contact-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.info-header, .progress-header, .contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .header-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
  }
  
  .booking-id {
    text {
      font-size: 24rpx;
      color: #999999;
    }
  }
}

.info-content {
  padding: 30rpx;
  
  .info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .info-label {
      font-size: 28rpx;
      color: #666666;
    }
    
    .info-value {
      font-size: 28rpx;
      color: #333333;
      text-align: right;
      flex: 1;
      margin-left: 20rpx;
      
      &.price {
        color: #FF6B81;
        font-weight: bold;
      }
      
      &.payment-unpaid {
        color: #ff4d4f;
        font-weight: bold;
      }
      
      &.payment-paid {
        color: #52c41a;
        font-weight: bold;
      }
      
      &.payment-refunded {
        color: #faad14;
        font-weight: bold;
      }
      
      &.payment-partial-refunded {
        color: #faad14;
        font-weight: bold;
      }
    }
  }
}

.progress-content {
  padding: 30rpx;
  
  .progress-step {
    display: flex;
    margin-bottom: 30rpx;
    opacity: 0.4;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &.active {
      opacity: 1;
    }
    
    .step-icon {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;
      
      .step-icon-text {
        font-size: 32rpx;
      }
    }
    
    .step-info {
      flex: 1;
      
      .step-title {
        font-size: 28rpx;
        color: #333333;
        font-weight: bold;
        margin-bottom: 10rpx;
        display: block;
      }
      
      .step-desc {
        font-size: 24rpx;
        color: #666666;
        display: block;
      }
    }
  }
}

.contact-content {
  padding: 30rpx;
  
  .contact-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .contact-icon {
      font-size: 40rpx;
      margin-right: 20rpx;
    }
    
    .contact-text {
      flex: 1;
      font-size: 28rpx;
      color: #333333;
    }
    
    .arrow-icon {
      font-size: 24rpx;
      color: #ccc;
    }
  }
}

.action-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  padding: 30rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    font-weight: bold;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    
    &.btn-default {
      background-color: #ffffff;
      color: #333333;
      border: 2rpx solid #e0e0e0;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    }
    
    &.btn-primary {
      background: linear-gradient(135deg, #FF6B81 0%, #FF8E9E 100%);
      color: #ffffff;
      box-shadow: 0 4rpx 12rpx rgba(255, 107, 129, 0.3);
    }
  }
}

/* 状态变更历史 */
.status-history {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  
  .history-header {
    margin-bottom: 30rpx;
    
    .header-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }
  
  .history-content {
    .history-item {
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .history-time {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 8rpx;
      }
      
      .history-status {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .history-reason {
        font-size: 26rpx;
        color: #666;
      }
    }
  }
}

/* 支付状态样式 */
.payment-unpaid {
  color: #ff6b6b;
  font-weight: bold;
}

.payment-paid {
  color: #51cf66;
  font-weight: bold;
}

.payment-refunded {
  color: #ffd43b;
  font-weight: bold;
}

.payment-partial-refunded {
  color: #ffa500;
  font-weight: bold;
}

/* 改期模态框 */
.reschedule-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  .modal-content {
    background: #fff;
    border-radius: 16rpx;
    width: 600rpx;
    max-height: 80vh;
    overflow-y: auto;
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;
      
      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }
      
      .modal-close {
        font-size: 40rpx;
        color: #999;
        cursor: pointer;
      }
    }
    
    .modal-body {
      padding: 30rpx;
      
      .form-item {
        margin-bottom: 30rpx;
        
        .form-label {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 16rpx;
          display: block;
        }
        
        .picker-input {
          background: #f8f9fa;
          border: 1rpx solid #e9ecef;
          border-radius: 8rpx;
          padding: 20rpx;
          font-size: 28rpx;
          color: #333;
          
          text {
            color: #666;
          }
        }
        
        .form-textarea {
          background: #f8f9fa;
          border: 1rpx solid #e9ecef;
          border-radius: 8rpx;
          padding: 20rpx;
          font-size: 28rpx;
          color: #333;
          min-height: 120rpx;
          width: 100%;
          box-sizing: border-box;
        }
      }
    }
    
    .modal-footer {
      display: flex;
      gap: 20rpx;
      padding: 30rpx;
      border-top: 1rpx solid #f0f0f0;
      
      .btn {
        flex: 1;
        height: 80rpx;
        border-radius: 8rpx;
        font-size: 28rpx;
        font-weight: 600;
        border: none;
        
        &.btn-default {
          background: #f8f9fa;
          color: #666;
        }
        
        &.btn-primary {
          background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
          color: white;
        }
      }
    }
  }
}
</style>
