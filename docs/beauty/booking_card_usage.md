# 套餐卡在预约中的使用功能

## 功能概述

在美容预约系统中，用户可以使用已购买的套餐卡来抵扣预约费用，实现更灵活的消费方式。

## 功能特性

### 1. 套餐卡类型支持
- **次数卡**：按次数使用，每次预约抵扣一次
- **金额卡**：按金额使用，可抵扣部分或全部费用
- **混合卡**：优先使用次数，次数用完后再使用金额

### 2. 使用规则
- 套餐卡必须在有效期内
- 套餐卡状态必须为正常（status = 1）
- 次数卡必须有剩余次数
- 金额卡必须有剩余金额
- 混合卡优先使用次数，次数用完后再使用金额

### 3. 价格计算逻辑
```
最终价格 = 服务价格 + 技师费用 - 套餐卡抵扣 - 优惠券抵扣
```

## 技术实现

### 1. 后端API接口

#### 1.1 获取用户套餐卡列表
```http
GET /api/beauty/card/my
Authorization: Bearer {token}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "card_name": "美容体验卡",
      "card_type": 1,
      "total_times": 5,
      "used_times": 2,
      "remaining_times": 3,
      "total_amount": 0,
      "used_amount": 0,
      "remaining_amount": 0,
      "valid_days": 90,
      "start_time": "2024-01-01",
      "end_time": "2024-04-01",
      "expire_time": "2024-04-01",
      "status": 1,
      "card_no": "UC123456789",
      "purchase_time": "2024-01-01 10:00:00"
    }
  ]
}
```

#### 1.2 创建预约（支持套餐卡）
```http
POST /api/beauty/booking
Authorization: Bearer {token}
Content-Type: application/json

{
  "service_id": 1,
  "technician_user_id": 1,
  "booking_date": "2024-01-15",
  "start_time": "09:00",
  "contact_name": "李小姐",
  "contact_phone": "13800138000",
  "special_requests": "希望技师轻一点",
  "coupon_id": 0,
  "user_card_id": 1
}
```

### 2. 前端实现

#### 2.1 预约表单修改
在 `bookify/pages/beauty/booking/form.vue` 中添加了套餐卡选择功能：

```vue
<!-- 套餐卡选择 -->
<view class="card-section">
  <view class="card-header" @click="showCardModal">
    <text class="header-title">套餐卡</text>
    <view class="card-value">
      <text class="card-text">{{ selectedCard ? selectedCard.card_name : '选择套餐卡' }}</text>
      <text class="arrow-icon">></text>
    </view>
  </view>
</view>
```

#### 2.2 套餐卡选择弹窗
```vue
<!-- 套餐卡选择弹窗 -->
<view class="card-modal" v-if="cardModalVisible" @click="closeCardModal">
  <view class="modal-content" @click.stop>
    <view class="modal-header">
      <text class="modal-title">选择套餐卡</text>
      <text class="close-icon" @click="closeCardModal">×</text>
    </view>
    <view class="card-list">
      <view 
        class="card-item" 
        :class="{ active: selectedCardId === null }"
        @click="selectCard(null)"
      >
        <text class="card-name">不使用套餐卡</text>
        <text class="check-icon" v-if="selectedCardId === null">✓</text>
      </view>
      <view 
        class="card-item" 
        :class="{ active: selectedCardId === card.id }"
        v-for="card in availableCards" 
        :key="card.id"
        @click="selectCard(card)"
      >
        <view class="card-info">
          <text class="card-name">{{ card.card_name }}</text>
          <text class="card-desc">
            <text v-if="card.card_type === 1">剩余次数：{{ card.remaining_times }}次</text>
            <text v-else-if="card.card_type === 2">剩余金额：¥{{ card.remaining_amount }}</text>
            <text v-else>剩余次数：{{ card.remaining_times }}次，剩余金额：¥{{ card.remaining_amount }}</text>
          </text>
          <text class="card-valid">有效期至：{{ card.expire_time }}</text>
        </view>
        <text class="check-icon" v-if="selectedCardId === card.id">✓</text>
      </view>
    </view>
  </view>
</view>
```

#### 2.3 价格计算逻辑
```javascript
computed: {
  finalPrice() {
    // 如果选择了套餐卡，价格为0
    if (this.selectedCard) {
      return 0
    }
    return Math.max(0, this.totalPrice - this.discountAmount)
  }
}
```

### 3. 数据库设计

#### 3.1 用户套餐卡表 (beauty_user_card)
```sql
CREATE TABLE beauty_user_card (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT COMMENT '关联订单ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    card_id BIGINT NOT NULL COMMENT '套餐卡ID',
    card_no VARCHAR(32) UNIQUE NOT NULL COMMENT '卡号',
    card_name VARCHAR(100) NOT NULL COMMENT '卡名称',
    card_type TINYINT NOT NULL COMMENT '卡类型：1-次数卡，2-金额卡，3-混合卡',
    
    -- 次数信息
    total_times INT DEFAULT 0 COMMENT '总次数',
    used_times INT DEFAULT 0 COMMENT '已使用次数',
    remaining_times INT DEFAULT 0 COMMENT '剩余次数',
    
    -- 金额信息
    total_amount DECIMAL(10,2) DEFAULT 0 COMMENT '总金额',
    used_amount DECIMAL(10,2) DEFAULT 0 COMMENT '已使用金额',
    remaining_amount DECIMAL(10,2) DEFAULT 0 COMMENT '剩余金额',
    
    -- 有效期信息
    valid_days INT COMMENT '有效天数',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    expire_time DATETIME COMMENT '过期时间',
    
    -- 状态信息
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，2-已过期，3-已用完',
    
    -- 系统字段
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_expire_time (expire_time)
) COMMENT='用户套餐卡表';
```

## 业务流程

### 1. 预约创建流程
1. 用户选择服务、技师和时间
2. 系统计算基础价格（服务价格 + 技师费用）
3. 用户选择套餐卡（可选）
4. 系统验证套餐卡有效性
5. 根据套餐卡类型计算抵扣金额
6. 计算最终价格
7. 创建预约记录
8. 更新套餐卡使用情况

### 2. 套餐卡验证流程
1. 检查套餐卡是否存在且属于当前用户
2. 检查套餐卡状态是否为正常
3. 检查套餐卡是否在有效期内
4. 根据套餐卡类型检查剩余资源：
   - 次数卡：检查剩余次数
   - 金额卡：检查剩余金额
   - 混合卡：优先检查剩余次数，再检查剩余金额

### 3. 套餐卡更新流程
1. 在预约创建成功后，更新套餐卡使用情况
2. 根据套餐卡类型更新相应字段：
   - 次数卡：减少剩余次数，增加已使用次数
   - 金额卡：减少剩余金额，增加已使用金额
   - 混合卡：优先更新次数，次数用完后再更新金额
3. 检查是否需要更新套餐卡状态（如已用完）

## 错误处理

### 1. 套餐卡相关错误
- `404` - 套餐卡不存在
- `400` - 套餐卡已过期
- `400` - 套餐卡次数已用完
- `400` - 套餐卡余额不足
- `400` - 套餐卡已用完

### 2. 前端错误提示
```javascript
// 根据错误类型显示不同提示
let errorMessage = '预约失败，请重试'

if (error.message.includes('套餐卡')) {
  errorMessage = '套餐卡使用失败，请检查套餐卡状态'
} else if (error.message.includes('时间冲突')) {
  errorMessage = '选择的时间段已被预约'
} else if (error.message.includes('余额不足')) {
  errorMessage = '账户余额不足，请充值'
}
```

## 使用示例

### 1. 用户购买套餐卡
1. 用户在套餐卡商城选择套餐卡
2. 完成支付购买
3. 系统创建用户套餐卡记录

### 2. 用户使用套餐卡预约
1. 用户进入预约页面
2. 选择服务、技师和时间
3. 点击"套餐卡"选择可用的套餐卡
4. 系统自动计算抵扣后的价格
5. 确认预约信息并提交
6. 系统创建预约并更新套餐卡使用情况

### 3. 查看套餐卡使用记录
1. 用户进入"我的套餐卡"页面
2. 查看套餐卡剩余次数/金额
3. 查看套餐卡使用记录

## 注意事项

1. **事务处理**：套餐卡使用和预约创建必须在同一个事务中，确保数据一致性
2. **并发控制**：套餐卡资源更新时需要考虑并发情况，避免超用
3. **状态管理**：套餐卡状态变更需要及时更新，避免使用已过期或已用完的套餐卡
4. **用户体验**：前端需要清晰显示套餐卡的使用情况和抵扣效果
5. **错误处理**：需要完善的错误处理机制，确保用户能够理解错误原因 