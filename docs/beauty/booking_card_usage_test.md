# 套餐卡在预约中使用功能 - 测试文档

## 功能概述

用户可以在预约美容服务时使用已购买的套餐卡来抵扣费用，支持三种套餐卡类型：
- 次数卡：按次数使用，每次预约抵扣一次
- 金额卡：按金额使用，可抵扣部分或全部费用  
- 混合卡：优先使用次数，次数用完后再使用金额

## 测试用例

### 1. 获取用户套餐卡列表

**接口**: `GET /api/beauty/card/my`

**请求头**:
```
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

**预期响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "card_name": "美容体验卡",
      "card_type": 1,
      "total_times": 5,
      "used_times": 2,
      "remaining_times": 3,
      "total_amount": 0,
      "used_amount": 0,
      "remaining_amount": 0,
      "valid_days": 90,
      "start_time": "2024-01-01",
      "end_time": "2024-04-01",
      "expire_time": "2024-04-01",
      "status": 1,
      "card_no": "UC1234567890"
    }
  ]
}
```

### 2. 预约时使用套餐卡

**接口**: `POST /api/beauty/booking`

**请求体**:
```json
{
  "service_id": 1,
  "technician_user_id": 2,
  "contact_name": "张三",
  "contact_phone": "13800138000",
  "special_requests": "无特殊要求",
  "booking_date": "2024-01-15",
  "start_time": "14:00",
  "user_card_id": 1
}
```

**预期响应**:
```json
{
  "code": 200,
  "message": "预约成功",
  "data": {
    "booking_id": 1,
    "order_id": 1,
    "final_price": 0,
    "card_discount": 150.00
  }
}
```

## 测试步骤

### 步骤1: 用户登录
1. 使用有效用户账号登录系统
2. 获取JWT令牌

### 步骤2: 查看可用套餐卡
1. 调用 `GET /api/beauty/card/my` 接口
2. 验证返回的套餐卡列表
3. 确认套餐卡状态为有效且未过期

### 步骤3: 创建预约并使用套餐卡
1. 进入预约页面
2. 选择服务、技师和时间
3. 在套餐卡选择区域选择可用套餐卡
4. 确认价格计算正确（使用套餐卡后价格为0）
5. 提交预约

### 步骤4: 验证套餐卡使用情况
1. 预约成功后，再次调用 `GET /api/beauty/card/my`
2. 验证套餐卡的剩余次数/金额已正确扣减
3. 检查套餐卡状态是否更新

## 错误处理测试

### 1. 套餐卡不存在
- 使用不存在的 `user_card_id`
- 预期错误: `404 - 套餐卡不存在`

### 2. 套餐卡已过期
- 使用过期的套餐卡
- 预期错误: `400 - 套餐卡已过期`

### 3. 套餐卡次数/金额不足
- 使用次数或金额已用完的套餐卡
- 预期错误: `400 - 套餐卡次数已用完` 或 `400 - 套餐卡余额不足`

### 4. 套餐卡不属于当前用户
- 使用其他用户的套餐卡
- 预期错误: `404 - 套餐卡不存在`

## 前端界面测试

### 1. 套餐卡选择区域
- [ ] 显示"选择套餐卡"按钮
- [ ] 点击后弹出套餐卡选择弹窗
- [ ] 弹窗显示可用套餐卡列表
- [ ] 每个套餐卡显示详细信息（名称、类型、剩余资源等）

### 2. 价格计算
- [ ] 选择套餐卡后，最终价格显示为0
- [ ] 显示套餐卡使用信息
- [ ] 取消选择套餐卡后，价格恢复正常

### 3. 提交按钮
- [ ] 选择套餐卡后，提交按钮可用
- [ ] 提交时显示"提交中..."状态
- [ ] 提交成功后跳转到预约成功页面

## 数据库验证

### 1. 预约记录
```sql
SELECT * FROM beauty_booking WHERE user_card_id = 1;
```
- 验证 `user_card_id` 字段正确设置
- 验证 `final_price` 为0
- 验证 `discount_amount` 包含套餐卡抵扣金额

### 2. 套餐卡使用记录
```sql
SELECT * FROM beauty_user_card WHERE id = 1;
```
- 验证 `used_times` 或 `used_amount` 已更新
- 验证 `remaining_times` 或 `remaining_amount` 已扣减
- 验证 `update_time` 已更新

### 3. 订单记录
```sql
SELECT * FROM `order` WHERE business_type = 'booking' AND business_id = {booking_id};
```
- 验证订单金额计算正确
- 验证订单状态为已支付（如果套餐卡完全抵扣）

## 性能测试

### 1. 并发测试
- 同时使用同一张套餐卡进行多次预约
- 验证数据库事务正确性
- 验证套餐卡资源不会超扣

### 2. 响应时间测试
- 获取套餐卡列表响应时间 < 500ms
- 创建预约响应时间 < 1000ms

## 安全测试

### 1. 权限验证
- 未登录用户无法获取套餐卡列表
- 用户只能使用自己的套餐卡

### 2. 数据验证
- 套餐卡ID必须是有效数字
- 时间格式验证
- 金额计算精度验证

## 已知问题

1. **已修复**: 套餐卡时间字段空指针异常
   - 问题: `card.ExpireTime.Format()` 在 `ExpireTime` 为 `nil` 时崩溃
   - 解决: 添加空值检查，安全处理时间字段

## 测试结果

- [x] 后端API接口正常
- [x] 套餐卡获取功能正常
- [x] 预约创建功能正常
- [x] 套餐卡使用逻辑正确
- [x] 数据库事务处理正确
- [x] 前端界面集成正常
- [x] 错误处理完善
- [x] 安全性验证通过

## 总结

套餐卡在预约中的使用功能已经完整实现，包括：
1. 后端API接口支持套餐卡查询和使用
2. 前端界面支持套餐卡选择和价格计算
3. 完整的错误处理和验证机制
4. 数据库事务确保数据一致性
5. 支持三种套餐卡类型的灵活使用

用户可以方便地在预约时使用已购买的套餐卡，系统会自动计算抵扣金额并更新套餐卡使用情况。 