package beauty

import (
	"encoding/json"
	"fmt"
	"math"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/do/BeautyDB"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// TechnicianService 技师服务层
type TechnicianService struct {
	db *gorm.DB
}

// NewTechnicianService 创建技师服务实例
func NewTechnicianService() *TechnicianService {
	return &TechnicianService{
		db: db.DB.Shop,
	}
}

// GetFeaturedTechnicians 获取明星技师列表
func (s *TechnicianService) GetFeaturedTechnicians(limit int) ([]dto.TechnicianItem, error) {
	var technicians []BeautyDB.BeautyTechnicianDO

	query := (&BeautyDB.BeautyTechnicianDO{}).Query().
		Where("is_delete = 0 AND status = 1").
		Order("is_featured DESC, rating_avg DESC, total_bookings DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&technicians).Error; err != nil {
		fmt.Printf("获取技师列表失败: %v\n", err)
		return nil, werror.New(500, "获取技师列表失败")
	}

	fmt.Printf("查询到 %d 个技师\n", len(technicians))

	items := make([]dto.TechnicianItem, 0, len(technicians))
	for _, tech := range technicians {
		fmt.Printf("处理技师: %s, ID: %d\n", tech.Name, tech.ID)
		item, err := s.convertToTechnicianItem(&tech)
		if err != nil {
			fmt.Printf("转换技师失败: %v\n", err)
			continue
		}
		items = append(items, *item)
	}

	fmt.Printf("最终返回 %d 个技师\n", len(items))
	return items, nil
}

// GetTechnicianList 获取技师列表
func (s *TechnicianService) GetTechnicianList(req *dto.BeautyTechnicianListRequest) (*dto.BeautyTechnicianListResponse, error) {
	var technicians []BeautyDB.BeautyTechnicianDO
	var total int64

	// 添加调试信息
	fmt.Printf("获取技师列表，请求参数: %+v\n", req)

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	query := (&BeautyDB.BeautyTechnicianDO{}).Query().
		Where("is_delete = 0 AND status = 1")

	// 服务筛选
	if req.ServiceID > 0 {
		fmt.Printf("筛选服务ID: %d\n", req.ServiceID)
		query = query.Where("JSON_CONTAINS(service_ids, ?)", fmt.Sprintf(`%d`, req.ServiceID))
	}

	// 等级筛选
	if req.Level != "" {
		query = query.Where("level = ?", req.Level)
	}

	// 关键词搜索
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("name LIKE ? OR introduction LIKE ?", keyword, keyword)
	}

	// 排序
	switch req.Sort {
	case "rating_desc":
		query = query.Order("rating_avg DESC")
	case "experience_desc":
		query = query.Order("experience DESC")
	case "review_count":
		query = query.Order("rating_count DESC")
	case "price_asc":
		query = query.Order("extra_fee ASC")
	case "price_desc":
		query = query.Order("extra_fee DESC")
	default:
		query = query.Order("is_featured DESC, rating_avg DESC, total_bookings DESC")
	}

	// 不要先统计总数，先查出所有技师，后面过滤无可用时间的技师再统计
	if err := query.Find(&technicians).Error; err != nil {
		fmt.Printf("查询技师列表失败: %v\n", err)
		return nil, werror.New(500, "获取技师列表失败")
	}
	fmt.Printf("查询到技师数量: %d\n", len(technicians))

	items := make([]dto.TechnicianItem, 0, len(technicians))
	for _, tech := range technicians {
		fmt.Printf("处理技师: %s, ID: %d, UserID: %d\n", tech.Name, tech.ID, tech.UserID)
		item, err := s.convertToTechnicianItem(&tech)
		if err != nil {
			fmt.Printf("转换技师失败: %v\n", err)
			continue
		}
		// 查询可预约时间
		if req.Date != "" {
			availableTime, err := s.GetTechnicianAvailableTime(uint(tech.UserID), req.Date, req.ServiceID)
			if err != nil {
				fmt.Printf("获取技师可用时间失败: %v\n", err)
				continue
			}
			// 只保留有可预约时间段的技师
			validSlots := make([]dto.TechnicianTimeSlot, 0)
			for _, slot := range availableTime.TimeSlots {
				if slot.Available {
					validSlots = append(validSlots, slot)
				}
			}
			if len(validSlots) == 0 {
				continue // 跳过无可用时间的技师
			}
			item.AvailableTimeSlots = validSlots
		}
		items = append(items, *item)
	}
	// 分页
	total = int64(len(items))
	start := (req.Page - 1) * req.PageSize
	end := start + req.PageSize
	if start > len(items) {
		start = len(items)
	}
	if end > len(items) {
		end = len(items)
	}
	pagedItems := items[start:end]

	return &dto.BeautyTechnicianListResponse{
		List:  pagedItems,
		Total: total,
		Page:  req.Page,
		Size:  req.PageSize,
	}, nil
}

// GetTechnicianDetail 获取技师详情
func (s *TechnicianService) GetTechnicianDetail(technicianUserID uint) (*dto.BeautyTechnicianDetailResponse, error) {
	var technician BeautyDB.BeautyTechnicianDO

	// 添加调试信息
	fmt.Printf("查询技师详情，user_id: %d\n", technicianUserID)

	// 先查询技师是否存在
	if err := s.db.Where("user_id = ? AND is_delete = 0", technicianUserID).First(&technician).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			fmt.Printf("技师不存在，user_id: %d\n", technicianUserID)
			return nil, werror.New(404, "技师不存在")
		}
		fmt.Printf("查询技师失败: %v\n", err)
		return nil, werror.New(500, "获取技师详情失败")
	}

	fmt.Printf("找到技师: %+v\n", technician)

	// 检查技师状态
	if technician.Status != 1 {
		fmt.Printf("技师状态不是启用状态，当前状态: %d\n", technician.Status)
		return nil, werror.New(404, "技师不存在")
	}

	// 转换为响应格式
	detail, err := s.convertToTechnicianDetail(&technician)
	if err != nil {
		return nil, err
	}

	return detail, nil
}

// GetTechnicianAvailableTime 获取技师可用时间
func (s *TechnicianService) GetTechnicianAvailableTime(technicianUserID uint, date string, serviceID uint) (*dto.TechnicianAvailableTimeResponse, error) {
	// 检查技师是否存在
	var technician BeautyDB.BeautyTechnicianDO
	if err := s.db.Where("user_id = ? AND is_delete = 0 AND status = 1", technicianUserID).First(&technician).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, werror.New(404, "技师不存在")
		}
		return nil, werror.New(500, "获取技师信息失败")
	}

	// 解析工作时间配置
	var workHours []map[string]string
	if technician.WorkHours != "" {
		if err := json.Unmarshal([]byte(technician.WorkHours), &workHours); err != nil {
			// 如果解析失败，使用默认工作时间
			workHours = []map[string]string{
				{"start": "09:00", "end": "12:00"},
				{"start": "14:00", "end": "18:00"},
			}
		}
	} else {
		// 默认工作时间
		workHours = []map[string]string{
			{"start": "09:00", "end": "12:00"},
			{"start": "14:00", "end": "18:00"},
		}
	}

	// 查询该日期的排班信息
	var schedules []BeautyDB.BeautyScheduleDO
	if err := s.db.Where("user_id = ? AND work_date = ? AND is_delete = 0 AND status = 1", technicianUserID, date).Find(&schedules).Error; err != nil {
		fmt.Printf("查询排班失败: %v\n", err)
	}

	// 查询该日期的预约冲突
	var bookings []BeautyDB.BeautyBookingDO
	if err := s.db.Where("technician_user_id = ? AND booking_date = ? AND booking_status IN ('confirmed', 'in_service') AND is_delete = 0", technicianUserID, date).Find(&bookings).Error; err != nil {
		fmt.Printf("查询预约失败: %v\n", err)
	}

	// 生成时间段
	timeSlots := []dto.TechnicianTimeSlot{}

	// 如果有排班信息，使用排班信息
	if len(schedules) > 0 {
		for _, schedule := range schedules {
			// 检查是否有预约冲突
			isAvailable := true
			for _, booking := range bookings {
				if s.isTimeConflict(schedule.StartTime, schedule.EndTime, booking.StartTime, booking.EndTime) {
					isAvailable = false
					break
				}
			}

			timeSlots = append(timeSlots, dto.TechnicianTimeSlot{
				StartTime: schedule.StartTime,
				EndTime:   schedule.EndTime,
				Available: isAvailable,
				Price:     technician.ExtraFee,
			})
		}
	} else {
		// 使用默认工作时间生成时间段
		for _, workHour := range workHours {
			startTime := workHour["start"]
			endTime := workHour["end"]

			// 生成30分钟间隔的时间段
			slots := s.generateTimeSlots(startTime, endTime, 30)

			for _, slot := range slots {
				// 检查是否有预约冲突
				isAvailable := true
				for _, booking := range bookings {
					if s.isTimeConflict(slot.StartTime, slot.EndTime, booking.StartTime, booking.EndTime) {
						isAvailable = false
						break
					}
				}

				timeSlots = append(timeSlots, dto.TechnicianTimeSlot{
					StartTime: slot.StartTime,
					EndTime:   slot.EndTime,
					Available: isAvailable,
					Price:     technician.ExtraFee,
				})
			}
		}
	}

	return &dto.TechnicianAvailableTimeResponse{
		Date:      date,
		TimeSlots: timeSlots,
	}, nil
}

// isTimeConflict 检查时间冲突
func (s *TechnicianService) isTimeConflict(scheduleStart, scheduleEnd, bookingStart, bookingEnd string) bool {
	// 简化版本：如果时间段有重叠则认为冲突
	// 实际应该解析时间字符串进行比较
	return scheduleStart < bookingEnd && scheduleEnd > bookingStart
}

// generateTimeSlots 生成时间段
func (s *TechnicianService) generateTimeSlots(startTime, endTime string, intervalMinutes int) []struct {
	StartTime string
	EndTime   string
} {
	var slots []struct {
		StartTime string
		EndTime   string
	}

	// 简化版本：生成固定的时间段
	// 实际应该解析时间字符串并计算
	slots = append(slots, struct {
		StartTime string
		EndTime   string
	}{
		StartTime: startTime,
		EndTime:   "09:30",
	})

	slots = append(slots, struct {
		StartTime string
		EndTime   string
	}{
		StartTime: "09:30",
		EndTime:   "10:00",
	})

	slots = append(slots, struct {
		StartTime string
		EndTime   string
	}{
		StartTime: "10:00",
		EndTime:   "10:30",
	})

	slots = append(slots, struct {
		StartTime string
		EndTime   string
	}{
		StartTime: "10:30",
		EndTime:   "11:00",
	})

	slots = append(slots, struct {
		StartTime string
		EndTime   string
	}{
		StartTime: "11:00",
		EndTime:   "11:30",
	})

	slots = append(slots, struct {
		StartTime string
		EndTime   string
	}{
		StartTime: "11:30",
		EndTime:   endTime,
	})

	return slots
}

// GetTechnicianProfile 获取技师个人信息
func (s *TechnicianService) GetTechnicianProfile(userID int64) (*dto.BeautyTechnicianProfileResponse, error) {
	// 查询技师信息
	var technician BeautyDB.BeautyTechnicianDO
	if err := s.db.Where("user_id = ? AND is_delete = 0", userID).First(&technician).Error; err != nil {
		return nil, err
	}

	// 查询用户信息（获取头像）
	var user ShopDB.UserDO
	var avatar string
	if err := s.db.Where("id = ?", userID).First(&user).Error; err == nil {
		avatar = user.Avatar
	}

	// 计算统计数据
	var totalServices int64
	var totalEarnings float64
	var satisfaction float64
	var ratingAvg float64

	// 总服务次数
	s.db.Model(&BeautyDB.BeautyBookingDO{}).Where("technician_user_id = ? AND booking_status = 'completed' AND is_delete = 0", userID).Count(&totalServices)

	// 总收益
	s.db.Model(&BeautyDB.BeautyBookingDO{}).Where("technician_user_id = ? AND booking_status = 'completed' AND is_delete = 0", userID).Select("SUM(final_price)").Row().Scan(&totalEarnings)

	// 满意度（平均评分）
	var bookingRatingAvg float64
	s.db.Model(&BeautyDB.BeautyBookingDO{}).Where("technician_user_id = ? AND booking_status = 'completed' AND rating > 0 AND is_delete = 0", userID).Select("AVG(rating)").Row().Scan(&bookingRatingAvg)

	// 平均评分：优先使用预约表的评分，如果没有则使用技师表的评分
	if bookingRatingAvg > 0 {
		ratingAvg = bookingRatingAvg
		satisfaction = bookingRatingAvg
	} else {
		ratingAvg = technician.RatingAvg
		satisfaction = technician.RatingAvg
	}

	// 保留两位小数
	ratingAvg = math.Round(ratingAvg*100) / 100
	satisfaction = math.Round(satisfaction*100) / 100

	return &dto.BeautyTechnicianProfileResponse{
		ID:            technician.ID,
		Name:          technician.Name,
		Level:         technician.Level,
		Avatar:        avatar,
		RatingAvg:     ratingAvg,
		Specialties:   technician.Specialties,
		Experience:    technician.Experience,
		Introduction:  technician.Introduction,
		TotalServices: int(totalServices),
		TotalEarnings: totalEarnings,
		Satisfaction:  satisfaction,
	}, nil
}

// UpdateTechnicianProfile 更新技师个人信息
func (s *TechnicianService) UpdateTechnicianProfile(userID int64, req *dto.BeautyTechnicianProfileRequest) error {
	// 更新技师工作信息（姓名、专业技能、工作经验、个人简介）
	updates := map[string]interface{}{}
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Specialties != "" {
		updates["specialties"] = req.Specialties
	}
	if req.Experience > 0 {
		updates["experience"] = req.Experience
	}
	if req.Introduction != "" {
		updates["introduction"] = req.Introduction
	}

	return s.db.Model(&BeautyDB.BeautyTechnicianDO{}).Where("user_id = ? AND is_delete = 0", userID).Updates(updates).Error
}

// GetTechnicianWorkSettings 获取技师工作设置
func (s *TechnicianService) GetTechnicianWorkSettings(userID int64) (*dto.BeautyTechnicianWorkSettingsResponse, error) {
	var technician BeautyDB.BeautyTechnicianDO
	if err := s.db.Where("user_id = ? AND is_delete = 0", userID).First(&technician).Error; err != nil {
		return nil, err
	}

	return &dto.BeautyTechnicianWorkSettingsResponse{
		IsOnline:  technician.Status == 1,
		WorkHours: technician.WorkHours,
	}, nil
}

// UpdateTechnicianWorkSettings 更新技师工作设置
func (s *TechnicianService) UpdateTechnicianWorkSettings(userID int64, req *dto.BeautyTechnicianWorkSettingsRequest) error {
	updates := map[string]interface{}{
		"work_hours": req.WorkHours,
	}

	return s.db.Model(&BeautyDB.BeautyTechnicianDO{}).Where("user_id = ? AND is_delete = 0", userID).Updates(updates).Error
}

// UploadTechnicianAvatar 上传技师头像
// 注意：技师头像现在统一在用户表中管理，此方法已废弃
func (s *TechnicianService) UploadTechnicianAvatar(userID int64, avatarURL string) error {
	// 技师头像现在统一在用户表中管理
	// 这里可以调用用户服务来更新头像
	return nil
}

// convertToTechnicianDetail 转换为技师详情
func (s *TechnicianService) convertToTechnicianDetail(tech *BeautyDB.BeautyTechnicianDO) (*dto.BeautyTechnicianDetailResponse, error) {
	// 解析专长数组
	var specialties []string
	if tech.Specialties != "" {
		if err := json.Unmarshal([]byte(tech.Specialties), &specialties); err != nil {
			specialties = []string{}
		}
	}

	// 解析服务ID数组
	var serviceIDs []uint
	if tech.ServiceIDs != "" {
		if err := json.Unmarshal([]byte(tech.ServiceIDs), &serviceIDs); err != nil {
			serviceIDs = []uint{}
		}
	}

	// 获取用户头像
	var avatar string
	var user ShopDB.UserDO
	if err := s.db.Where("id = ?", tech.UserID).First(&user).Error; err == nil {
		avatar = user.Avatar
	}

	// 动态计算评价和预约数据
	var ratingAvg float64
	var ratingCount int64
	var totalBookings int64
	var completedBookings int64

	// 从预约表统计数据
	s.db.Model(&BeautyDB.BeautyBookingDO{}).
		Where("technician_user_id = ? AND is_delete = 0", tech.UserID).
		Select("COUNT(*)").Row().Scan(&totalBookings)

	s.db.Model(&BeautyDB.BeautyBookingDO{}).
		Where("technician_user_id = ? AND booking_status = 'completed' AND is_delete = 0", tech.UserID).
		Select("COUNT(*)").Row().Scan(&completedBookings)

	s.db.Model(&BeautyDB.BeautyBookingDO{}).
		Where("technician_user_id = ? AND booking_status = 'completed' AND rating > 0 AND is_delete = 0", tech.UserID).
		Select("AVG(rating), COUNT(*)").Row().Scan(&ratingAvg, &ratingCount)

	// 如果没有评价数据，显示0条评价，评分显示为0
	if ratingCount == 0 {
		ratingAvg = 0
		ratingCount = 0
	}

	// 保留两位小数
	ratingAvg = math.Round(ratingAvg*100) / 100

	return &dto.BeautyTechnicianDetailResponse{
		ID:                tech.ID,
		Name:              tech.Name,
		Level:             tech.Level,
		Experience:        tech.Experience,
		Specialties:       specialties,
		Introduction:      tech.Introduction,
		ServiceIDs:        serviceIDs,
		WorkHours:         tech.WorkHours,
		Avatar:            avatar,
		TotalBookings:     int(totalBookings),
		CompletedBookings: int(completedBookings),
		RatingAvg:         ratingAvg,
		RatingCount:       int(ratingCount),
		ExtraFee:          tech.ExtraFee,
		Status:            tech.Status,
		IsFeatured:        tech.IsFeatured == 1,
		AvailableServices: []dto.BeautyServiceItem{}, // 暂时为空，后续可以查询
	}, nil
}

// convertToTechnicianItem 转换为技师项
func (s *TechnicianService) convertToTechnicianItem(tech *BeautyDB.BeautyTechnicianDO) (*dto.TechnicianItem, error) {
	// 解析专长数组
	var specialties []string
	if tech.Specialties != "" {
		if err := json.Unmarshal([]byte(tech.Specialties), &specialties); err != nil {
			fmt.Printf("解析专长失败: %v, 原始数据: %s\n", err, tech.Specialties)
			specialties = []string{}
		}
	}

	// 获取用户头像
	var avatar string
	var user ShopDB.UserDO
	if err := s.db.Where("id = ?", tech.UserID).First(&user).Error; err == nil {
		avatar = user.Avatar
	}

	// 动态计算评价数据
	var ratingAvg float64
	var ratingCount int64

	// 从预约表统计评分数据
	s.db.Model(&BeautyDB.BeautyBookingDO{}).
		Where("technician_user_id = ? AND booking_status = 'completed' AND rating > 0 AND is_delete = 0", tech.UserID).
		Select("AVG(rating), COUNT(*)").Row().Scan(&ratingAvg, &ratingCount)

	// 如果没有评价数据，显示0条评价，评分显示为0
	if ratingCount == 0 {
		ratingAvg = 0
		ratingCount = 0
	}

	// 保留两位小数
	ratingAvg = math.Round(ratingAvg*100) / 100

	return &dto.TechnicianItem{
		ID:           tech.ID,
		UserID:       uint(tech.UserID), // 添加用户ID
		Name:         tech.Name,
		Level:        tech.Level,
		Experience:   tech.Experience,
		Specialties:  specialties,
		Introduction: tech.Introduction,
		RatingAvg:    ratingAvg,
		RatingCount:  int(ratingCount),
		ExtraFee:     tech.ExtraFee,
		IsFeatured:   tech.IsFeatured == 1,
		Avatar:       avatar, // 添加头像
	}, nil
}
