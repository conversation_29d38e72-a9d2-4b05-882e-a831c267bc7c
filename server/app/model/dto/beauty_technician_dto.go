package dto

// BeautyTechnicianListRequest 技师列表请求
type BeautyTechnicianListRequest struct {
	Page      int    `form:"page" binding:"min=1"`              // 页码
	PageSize  int    `form:"page_size" binding:"min=1,max=100"` // 每页数量
	ServiceID uint   `form:"service_id"`                        // 服务ID筛选
	Level     string `form:"level"`                             // 技师等级筛选
	Sort      string `form:"sort"`                              // 排序方式
	Keyword   string `form:"keyword"`                           // 关键词搜索
	Date      string `form:"date" json:"date"`                  // 新增：指定日期
}

// BeautyTechnicianListResponse 技师列表响应
type BeautyTechnicianListResponse struct {
	List  []TechnicianItem `json:"list"`
	Total int64            `json:"total"`
	Page  int              `json:"page"`
	Size  int              `json:"size"`
}

// BeautyTechnicianDetailResponse 技师详情响应
type BeautyTechnicianDetailResponse struct {
	ID           uint     `json:"id"`
	Name         string   `json:"name"`
	Level        string   `json:"level"`
	Experience   int      `json:"experience"`
	Specialties  []string `json:"specialties"`
	Introduction string   `json:"introduction"`
	ServiceIDs   []uint   `json:"service_ids"`
	WorkHours    string   `json:"work_hours"`
	Avatar       string   `json:"avatar"` // 头像URL

	// 统计数据
	TotalBookings     int     `json:"total_bookings"`
	CompletedBookings int     `json:"completed_bookings"`
	RatingAvg         float64 `json:"rating_avg"`
	RatingCount       int     `json:"rating_count"`

	// 价格设置
	ExtraFee float64 `json:"extra_fee"`

	// 状态
	Status     int  `json:"status"`
	IsFeatured bool `json:"is_featured"`

	// 可提供的服务
	AvailableServices []BeautyServiceItem `json:"available_services"`
}

// TechnicianAvailableTimeResponse 技师可用时间响应
type TechnicianAvailableTimeResponse struct {
	Date      string               `json:"date"`
	TimeSlots []TechnicianTimeSlot `json:"time_slots"`
}

// TechnicianTimeSlot 技师时间段
type TechnicianTimeSlot struct {
	StartTime string  `json:"start_time"`
	EndTime   string  `json:"end_time"`
	Available bool    `json:"available"`
	Price     float64 `json:"price"`
}

// TechnicianItem 技师项
type TechnicianItem struct {
	ID                 uint                 `json:"id"`
	UserID             uint                 `json:"user_id"` // 新增：用户ID
	Name               string               `json:"name"`
	Level              string               `json:"level"`
	Experience         int                  `json:"experience"`
	Specialties        []string             `json:"specialties"`
	Introduction       string               `json:"introduction"`
	ServiceIDs         []uint               `json:"service_ids"`
	WorkHours          string               `json:"work_hours"`
	Avatar             string               `json:"avatar"`               // 头像URL
	AvailableTimeSlots []TechnicianTimeSlot `json:"available_time_slots"` // 新增：可预约时间段

	// 统计数据
	TotalBookings     int     `json:"total_bookings"`
	CompletedBookings int     `json:"completed_bookings"`
	RatingAvg         float64 `json:"rating_avg"`
	RatingCount       int     `json:"rating_count"`

	// 价格设置
	ExtraFee float64 `json:"extra_fee"`

	// 状态
	Status     int  `json:"status"`
	IsFeatured bool `json:"is_featured"`

	// 可提供的服务
	AvailableServices []BeautyServiceItem `json:"available_services"`
}

// BeautyTechnicianProfileRequest 技师个人信息更新请求
type BeautyTechnicianProfileRequest struct {
	Name         string `json:"name,omitempty"` // 非必填
	Specialties  string `json:"specialties"`    // 可单独提交
	Experience   int    `json:"experience,omitempty"`
	Introduction string `json:"introduction,omitempty"`
}

// BeautyTechnicianWorkSettingsRequest 技师工作设置更新请求
type BeautyTechnicianWorkSettingsRequest struct {
	WorkHours string `json:"work_hours"` // 工作时间，JSON格式
}

// BeautyTechnicianProfileResponse 技师个人信息响应
type BeautyTechnicianProfileResponse struct {
	ID            uint    `json:"id"`
	Name          string  `json:"name"`
	Level         string  `json:"level"`
	Avatar        string  `json:"avatar"`     // 头像URL
	RatingAvg     float64 `json:"rating_avg"` // 平均评分
	Specialties   string  `json:"specialties"`
	Experience    int     `json:"experience"`
	Introduction  string  `json:"introduction"`
	TotalServices int     `json:"total_services"`
	TotalEarnings float64 `json:"total_earnings"`
	Satisfaction  float64 `json:"satisfaction"`
}

// BeautyTechnicianWorkSettingsResponse 技师工作设置响应
type BeautyTechnicianWorkSettingsResponse struct {
	IsOnline  bool   `json:"is_online"`
	WorkHours string `json:"work_hours"`
}
